rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Allow authenticated users to read and write their own user document
    match /users/{userId} {
      allow read, write, create, update, delete: if request.auth != null && request.auth.uid == userId;
    }
    
    // Allow authenticated users to read goal and equipment master lists
    match /goals_master_list/{document=**} {
      allow read: if request.auth != null;
    }
    
    match /equipment_master_list/{document=**} {
      allow read: if request.auth != null;
    }
  }
}