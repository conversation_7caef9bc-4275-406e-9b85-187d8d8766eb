{"collections": {"goals_master_list": [{"goalKey": "build_muscle", "title": "Build Muscle Mass and Size", "description": "Recommended For: Those looking to increase muscle size, strength, and definition through progressive resistance training.", "requiresSportSpecification": false}, {"goalKey": "calisthenics", "title": "Calisthenics & Bodyweight Mastery", "description": "Recommended For: Athletes wanting to master bodyweight movements, improve functional strength, and achieve impressive skills like muscle-ups and handstands.", "requiresSportSpecification": false}, {"goalKey": "weight_loss", "title": "Weight Loss & Fat Burning", "description": "Recommended For: Individuals focused on reducing body fat, improving body composition, and achieving sustainable weight management.", "requiresSportSpecification": false}, {"goalKey": "optimize_health", "title": "Optimize Overall Health", "description": "Recommended For: People prioritizing longevity, disease prevention, and overall wellness through balanced fitness and nutrition.", "requiresSportSpecification": false}, {"goalKey": "cardio_bunny", "title": "Cardio Endurance & Stamina", "description": "Recommended For: Runners, cyclists, and endurance athletes looking to improve cardiovascular fitness and stamina.", "requiresSportSpecification": false}, {"goalKey": "increase_strength", "title": "Increase Pure Strength", "description": "Recommended For: Powerlifters and strength athletes focused on maximizing their one-rep max in key lifts.", "requiresSportSpecification": false}, {"goalKey": "training_for_sport", "title": "Training for Specific Sport/Activity", "description": "Recommended For: Athletes preparing for a particular sport, competition, or physical challenge requiring specialized training.", "requiresSportSpecification": true}], "equipment_master_list": [{"equipmentKey": "bodyweight_only", "name": "Bodyweight Only", "category": "No Equipment"}, {"equipmentKey": "dumbbell_set_light", "name": "<PERSON><PERSON><PERSON><PERSON> (Light Set)", "category": "Small Weights"}, {"equipmentKey": "dumbbell_set_heavy", "name": "<PERSON><PERSON><PERSON><PERSON> (Heavy Set)", "category": "Small Weights"}, {"equipmentKey": "kettlebell_16kg", "name": "<PERSON><PERSON><PERSON> (16kg)", "category": "Small Weights"}, {"equipmentKey": "kettlebell_24kg", "name": "<PERSON><PERSON><PERSON> (24kg)", "category": "Small Weights"}, {"equipmentKey": "resistance_bands", "name": "Resistance Bands", "category": "Small Weights"}, {"equipmentKey": "barbell_olympic", "name": "Olympic Barbell", "category": "Bars & Plates"}, {"equipmentKey": "weight_plates", "name": "Weight Plates", "category": "Bars & Plates"}, {"equipmentKey": "ez_curl_bar", "name": "EZ Curl Bar", "category": "Bars & Plates"}, {"equipmentKey": "bench_adjustable", "name": "Adjustable <PERSON><PERSON>", "category": "Benches & Racks"}, {"equipmentKey": "squat_rack", "name": "<PERSON><PERSON><PERSON>", "category": "Benches & Racks"}, {"equipmentKey": "power_rack", "name": "Power Rack", "category": "Benches & Racks"}, {"equipmentKey": "pull_up_bar", "name": "Pull-up Bar", "category": "Cardio & Accessories"}, {"equipmentKey": "treadmill", "name": "Treadmill", "category": "Cardio & Accessories"}, {"equipmentKey": "stationary_bike", "name": "Stationary Bike", "category": "Cardio & Accessories"}]}}