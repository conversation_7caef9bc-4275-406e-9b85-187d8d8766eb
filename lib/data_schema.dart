// Data schema definitions for Firestore collections

class UserProfile {
  final String userId;
  final String email;
  final String name;
  final String? photoUrl;
  final DateTime createdAt;
  final DateTime updatedAt;
  final bool onboardingCompleted;
  final PersonalInfo personalInfo;
  final FitnessProfile fitnessProfile;
  final WorkoutPreferences workoutPreferences;
  final PrivacyPreferences privacyPreferences;

  UserProfile({
    required this.userId,
    required this.email,
    required this.name,
    this.photoUrl,
    required this.createdAt,
    required this.updatedAt,
    required this.onboardingCompleted,
    required this.personalInfo,
    required this.fitnessProfile,
    required this.workoutPreferences,
    required this.privacyPreferences,
  });

  Map<String, dynamic> toJson() {
    return {
      'userId': userId,
      'email': email,
      'name': name,
      'photoUrl': photoUrl,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
      'onboardingCompleted': onboardingCompleted,
      'personalInfo': personalInfo.toJson(),
      'fitnessProfile': fitnessProfile.toJson(),
      'workoutPreferences': workoutPreferences.toJson(),
      'privacyPreferences': privacyPreferences.toJson(),
    };
  }

  factory UserProfile.fromJson(Map<String, dynamic> json) {
    return UserProfile(
      userId: json['userId'],
      email: json['email'],
      name: json['name'],
      photoUrl: json['photoUrl'],
      createdAt: DateTime.parse(json['createdAt']),
      updatedAt: DateTime.parse(json['updatedAt']),
      onboardingCompleted: json['onboardingCompleted'],
      personalInfo: PersonalInfo.fromJson(json['personalInfo']),
      fitnessProfile: FitnessProfile.fromJson(json['fitnessProfile']),
      workoutPreferences: WorkoutPreferences.fromJson(json['workoutPreferences']),
      privacyPreferences: PrivacyPreferences.fromJson(json['privacyPreferences']),
    );
  }
}

class PersonalInfo {
  final String gender;
  final DateTime dateOfBirth;
  final double height;
  final double weight;
  final String preferredUnits;

  PersonalInfo({
    required this.gender,
    required this.dateOfBirth,
    required this.height,
    required this.weight,
    required this.preferredUnits,
  });

  Map<String, dynamic> toJson() {
    return {
      'gender': gender,
      'dateOfBirth': dateOfBirth.toIso8601String(),
      'height': height,
      'weight': weight,
      'preferredUnits': preferredUnits,
    };
  }

  factory PersonalInfo.fromJson(Map<String, dynamic> json) {
    return PersonalInfo(
      gender: json['gender'],
      dateOfBirth: DateTime.parse(json['dateOfBirth']),
      height: json['height'].toDouble(),
      weight: json['weight'].toDouble(),
      preferredUnits: json['preferredUnits'],
    );
  }
}

class FitnessProfile {
  final String weightliftingLevel;
  final String cardioEnduranceLevel;
  final String? additionalFitnessInfo;
  final List<FitnessGoal> primaryGoals;
  final String? additionalGoalInfo;

  FitnessProfile({
    required this.weightliftingLevel,
    required this.cardioEnduranceLevel,
    this.additionalFitnessInfo,
    required this.primaryGoals,
    this.additionalGoalInfo,
  });

  Map<String, dynamic> toJson() {
    return {
      'weightliftingLevel': weightliftingLevel,
      'cardioEnduranceLevel': cardioEnduranceLevel,
      'additionalFitnessInfo': additionalFitnessInfo,
      'primaryGoals': primaryGoals.map((g) => g.toJson()).toList(),
      'additionalGoalInfo': additionalGoalInfo,
    };
  }

  factory FitnessProfile.fromJson(Map<String, dynamic> json) {
    return FitnessProfile(
      weightliftingLevel: json['weightliftingLevel'],
      cardioEnduranceLevel: json['cardioEnduranceLevel'],
      additionalFitnessInfo: json['additionalFitnessInfo'],
      primaryGoals: (json['primaryGoals'] as List)
          .map((g) => FitnessGoal.fromJson(g))
          .toList(),
      additionalGoalInfo: json['additionalGoalInfo'],
    );
  }
}

class FitnessGoal {
  final String goalKey;
  final int priority;
  final String? details;

  FitnessGoal({
    required this.goalKey,
    required this.priority,
    this.details,
  });

  Map<String, dynamic> toJson() {
    return {
      'goalKey': goalKey,
      'priority': priority,
      'details': details,
    };
  }

  factory FitnessGoal.fromJson(Map<String, dynamic> json) {
    return FitnessGoal(
      goalKey: json['goalKey'],
      priority: json['priority'],
      details: json['details'],
    );
  }
}

class WorkoutPreferences {
  final List<String> availableEquipment;
  final WorkoutFrequency workoutFrequency;
  final SessionLength sessionLength;
  final List<String> lastWorkedMuscleGroups;

  WorkoutPreferences({
    required this.availableEquipment,
    required this.workoutFrequency,
    required this.sessionLength,
    required this.lastWorkedMuscleGroups,
  });

  Map<String, dynamic> toJson() {
    return {
      'availableEquipment': availableEquipment,
      'workoutFrequency': workoutFrequency.toJson(),
      'sessionLength': sessionLength.toJson(),
      'lastWorkedMuscleGroups': lastWorkedMuscleGroups,
    };
  }

  factory WorkoutPreferences.fromJson(Map<String, dynamic> json) {
    return WorkoutPreferences(
      availableEquipment: List<String>.from(json['availableEquipment']),
      workoutFrequency: WorkoutFrequency.fromJson(json['workoutFrequency']),
      sessionLength: SessionLength.fromJson(json['sessionLength']),
      lastWorkedMuscleGroups: List<String>.from(json['lastWorkedMuscleGroups']),
    );
  }
}

class WorkoutFrequency {
  final String type;
  final List<String>? days;
  final int? count;

  WorkoutFrequency({
    required this.type,
    this.days,
    this.count,
  });

  Map<String, dynamic> toJson() {
    return {
      'type': type,
      'days': days,
      'count': count,
    };
  }

  factory WorkoutFrequency.fromJson(Map<String, dynamic> json) {
    return WorkoutFrequency(
      type: json['type'],
      days: json['days'] != null ? List<String>.from(json['days']) : null,
      count: json['count'],
    );
  }
}

class SessionLength {
  final String preferenceType;
  final int? durationMinutes;

  SessionLength({
    required this.preferenceType,
    this.durationMinutes,
  });

  Map<String, dynamic> toJson() {
    return {
      'preferenceType': preferenceType,
      'durationMinutes': durationMinutes,
    };
  }

  factory SessionLength.fromJson(Map<String, dynamic> json) {
    return SessionLength(
      preferenceType: json['preferenceType'],
      durationMinutes: json['durationMinutes'],
    );
  }
}

class PrivacyPreferences {
  final String locationAccess;
  final String dataUsageForPersonalization;

  PrivacyPreferences({
    required this.locationAccess,
    required this.dataUsageForPersonalization,
  });

  Map<String, dynamic> toJson() {
    return {
      'locationAccess': locationAccess,
      'dataUsageForPersonalization': dataUsageForPersonalization,
    };
  }

  factory PrivacyPreferences.fromJson(Map<String, dynamic> json) {
    return PrivacyPreferences(
      locationAccess: json['locationAccess'],
      dataUsageForPersonalization: json['dataUsageForPersonalization'],
    );
  }
}