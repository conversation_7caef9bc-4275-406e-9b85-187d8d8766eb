import 'dart:convert';
import 'package:http/http.dart' as http;

class AIService {
  static const String _apiKey = 'OPENAI-API-KEY';
  static const String _baseUrl = 'https://api.openai.com/v1/chat/completions';

  static Future<String> getConversationalResponse({
    required String userMessage,
    required String context,
    String? userName,
  }) async {
    try {
      final messages = [
        {
          'role': 'system',
          'content': '''You are Coach AI, a friendly and motivating personal fitness coach. You're guiding a user through their fitness onboarding process. 

Context: $context

Your personality:
- Enthusiastic and supportive
- Professional but friendly
- Use encouraging language
- Keep responses concise (1-2 sentences max)
- Use fitness expertise to provide helpful insights
- Remember you're helping them set up their fitness journey

${userName != null ? "The user's name is $userName. Use their name occasionally to personalize responses." : ""}

Respond in a conversational, coach-like manner that makes the user feel supported and motivated.'''
        },
        {
          'role': 'user',
          'content': userMessage,
        }
      ];

      final response = await http.post(
        Uri.parse(_baseUrl),
        headers: {
          'Authorization': 'Bearer $_apiKey',
          'Content-Type': 'application/json',
        },
        body: jsonEncode({
          'model': 'gpt-4o-mini',
          'messages': messages,
          'max_tokens': 150,
          'temperature': 0.7,
        }),
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(utf8.decode(response.bodyBytes));
        return data['choices'][0]['message']['content'].trim();
      } else {
        throw Exception('Failed to get AI response: ${response.statusCode}');
      }
    } catch (e) {
      print('AI Service Error: $e');
      return _getFallbackResponse(context);
    }
  }

  static Future<String> getGoalExplanation(String goalKey, String goalTitle) async {
    try {
      final messages = [
        {
          'role': 'system',
          'content': '''You are Coach AI explaining fitness goals. Provide a brief, encouraging explanation of this fitness goal in 1-2 sentences. Focus on benefits and who this goal is perfect for. Keep it motivational and informative.'''
        },
        {
          'role': 'user',
          'content': 'Explain the fitness goal: $goalTitle (goalKey: $goalKey)',
        }
      ];

      final response = await http.post(
        Uri.parse(_baseUrl),
        headers: {
          'Authorization': 'Bearer $_apiKey',
          'Content-Type': 'application/json',
        },
        body: jsonEncode({
          'model': 'gpt-4o-mini',
          'messages': messages,
          'max_tokens': 100,
          'temperature': 0.7,
        }),
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(utf8.decode(response.bodyBytes));
        return data['choices'][0]['message']['content'].trim();
      } else {
        throw Exception('Failed to get AI response: ${response.statusCode}');
      }
    } catch (e) {
      print('AI Service Error: $e');
      return _getFallbackGoalExplanation(goalKey);
    }
  }

  static Future<String> generateOnboardingSummary({
    required String userName,
    required Map<String, dynamic> userProfile,
  }) async {
    try {
      final messages = [
        {
          'role': 'system',
          'content': '''You are Coach AI providing an encouraging onboarding completion summary. Based on the user's profile data, create a personalized, motivational message that:

1. Acknowledges their goals and commitment
2. Highlights what makes their fitness journey unique
3. Builds excitement for what's ahead
4. Keep it to 2-3 sentences max
5. Sound enthusiastic and supportive

Make it feel personal and inspiring.'''
        },
        {
          'role': 'user',
          'content': 'Create an onboarding completion summary for $userName based on their profile: ${jsonEncode(userProfile)}',
        }
      ];

      final response = await http.post(
        Uri.parse(_baseUrl),
        headers: {
          'Authorization': 'Bearer $_apiKey',
          'Content-Type': 'application/json',
        },
        body: jsonEncode({
          'model': 'gpt-4o',
          'messages': messages,
          'max_tokens': 200,
          'temperature': 0.8,
        }),
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(utf8.decode(response.bodyBytes));
        return data['choices'][0]['message']['content'].trim();
      } else {
        throw Exception('Failed to get AI response: ${response.statusCode}');
      }
    } catch (e) {
      print('AI Service Error: $e');
      return 'Fantastic, $userName! We\'re all set and I\'m excited to be your fitness coach. Based on your goals and preferences, I\'ve got everything I need to create an amazing personalized plan just for you!';
    }
  }

  static String _getFallbackResponse(String context) {
    final fallbacks = [
      'That sounds great! Let\'s keep moving forward with your fitness journey.',
      'Perfect! I\'m here to support you every step of the way.',
      'Excellent choice! This information helps me create the best plan for you.',
      'Great! I\'m learning so much about how to help you succeed.',
      'Wonderful! Every detail helps me become a better coach for you.',
    ];
    return fallbacks[(context.length % fallbacks.length)];
  }

  static String _getFallbackGoalExplanation(String goalKey) {
    switch (goalKey) {
      case 'build_muscle':
        return 'Perfect for building strength and size through progressive resistance training!';
      case 'weight_loss':
        return 'Great choice for sustainable fat loss and improved body composition!';
      case 'cardio_bunny':
        return 'Excellent for building endurance and cardiovascular health!';
      case 'increase_strength':
        return 'Ideal for maximizing your lifting power and personal records!';
      case 'calisthenics':
        return 'Amazing for bodyweight mastery and functional strength!';
      case 'optimize_health':
        return 'Perfect for overall wellness and long-term health benefits!';
      case 'training_for_sport':
        return 'Excellent for sport-specific performance and competition prep!';
      default:
        return 'This is a fantastic goal that will transform your fitness journey!';
    }
  }
}