import 'package:flutter/material.dart';
import 'package:smooth_page_indicator/smooth_page_indicator.dart';
import 'ai_intro_page.dart';
import 'personal_info_page.dart';
import '../services/auth_service.dart';
import '../services/ai_service.dart';

class OnboardingFlow extends StatefulWidget {
  const OnboardingFlow({super.key});

  @override
  State<OnboardingFlow> createState() => _OnboardingFlowState();
}

class _OnboardingFlowState extends State<OnboardingFlow> {
  final PageController _pageController = PageController();
  int _currentPage = 0;
  
  // Collected data
  Map<String, dynamic> _personalInfo = {};
  Map<String, dynamic> _fitnessProfile = {};
  Map<String, dynamic> _workoutPreferences = {};

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  void _nextPage() {
    if (_currentPage < 6) {
      setState(() {
        _currentPage++;
      });
      _pageController.nextPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  void _onPersonalInfoComplete(Map<String, dynamic> data) {
    setState(() {
      _personalInfo = data;
    });
    _nextPage();
  }

  void _onFitnessProfileComplete(Map<String, dynamic> data) {
    setState(() {
      _fitnessProfile = data;
    });
    _nextPage();
  }

  void _onWorkoutPreferencesComplete(Map<String, dynamic> data) {
    setState(() {
      _workoutPreferences = data;
    });
    _completeOnboarding();
  }

  Future<void> _completeOnboarding() async {
    // Save to Firebase
    final profileData = {
      'personalInfo': _personalInfo,
      'fitnessProfile': _fitnessProfile,
      'workoutPreferences': _workoutPreferences,
      'onboardingCompleted': true,
    };

    final success = await AuthService.updateUserProfile(profileData);
    
    if (success && mounted) {
      // Generate AI completion summary
      final summary = await AIService.generateOnboardingSummary(
        userName: _personalInfo['name'] ?? 'there',
        userProfile: profileData,
      );
      
      _showCompletionDialog(summary);
    }
  }

  void _showCompletionDialog(String summary) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title: const Text('🎉 Welcome to Your Fitness Journey!'),
        content: Text(summary),
        actions: [
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              // Navigate to main app
            },
            child: const Text('Let\'s Go!'),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Scaffold(
      body: Stack(
        children: [
          PageView(
            controller: _pageController,
            physics: const NeverScrollableScrollPhysics(),
            onPageChanged: (index) {
              setState(() {
                _currentPage = index;
              });
            },
            children: [
              AIIntroPage(onContinue: _nextPage),
              PersonalInfoPage(onComplete: _onPersonalInfoComplete),
              // TODO: Add remaining pages
              // FitnessLevelPage(onComplete: _onFitnessProfileComplete),
              // GoalsSelectionPage(onComplete: _onFitnessProfileComplete),
              // EquipmentPage(onComplete: _onWorkoutPreferencesComplete),
              // SchedulePage(onComplete: _onWorkoutPreferencesComplete),
              
              // Temporary placeholder for testing
              Scaffold(
                body: Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(
                        'More pages coming soon...',
                        style: theme.textTheme.headlineMedium,
                      ),
                      const SizedBox(height: 20),
                      ElevatedButton(
                        onPressed: () => _completeOnboarding(),
                        child: const Text('Complete (Test)'),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
          
          // Progress indicator
          if (_currentPage > 0)
            Positioned(
              top: 60,
              left: 0,
              right: 0,
              child: Center(
                child: SmoothPageIndicator(
                  controller: _pageController,
                  count: 7, // Total pages including intro
                  effect: WormEffect(
                    dotHeight: 8,
                    dotWidth: 8,
                    spacing: 16,
                    activeDotColor: theme.colorScheme.primary,
                    dotColor: theme.colorScheme.outline.withOpacity(0.3),
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }
}